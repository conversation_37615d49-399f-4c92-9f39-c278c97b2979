# 🚀 Quick API Reference Card

## 📍 Server Addresses

Replace `YOUR_SERVER` with one of these:

| Environment | Address | Example |
|------------|---------|---------|
| **Local (same machine)** | `http://localhost:5000` | `http://localhost:5000/api/user-profiles` |
| **Local Network (WiFi)** | `http://YOUR_LOCAL_IP:5000` | `http://*************:5000/api/user-profiles` |
| **Public Server** | `http://your-domain.com` | `http://api.example.com/api/user-profiles` |
| **Vercel/Cloud** | `https://your-app.vercel.app` | `https://my-app.vercel.app/api/user-profiles` |

### 🔍 Find Your Local IP

```bash
# Linux/Mac
hostname -I | awk '{print $1}'

# Windows
ipconfig | findstr IPv4
```

---

## 🔑 Authentication

### 1. Get Token (Login)

```bash
curl -X POST YOUR_SERVER/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": { "username": "admin", "role": "admin" }
  }
}
```

### 2. Use Token

```bash
# Save token
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# Use in requests
curl -H "Authorization: Bearer $TOKEN" YOUR_SERVER/api/ENDPOINT
```

---

## 📋 Common API Endpoints

### User Profiles

#### Get All Profiles
```bash
curl -H "Authorization: Bearer $TOKEN" \
  YOUR_SERVER/api/user-profiles
```

#### Get Single Profile
```bash
curl -H "Authorization: Bearer $TOKEN" \
  YOUR_SERVER/api/user-profiles/PROFILE_ID
```

#### Create Profile
```bash
curl -X POST YOUR_SERVER/api/user-profiles \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "1234567890",
    "address": "123 Main St"
  }'
```

#### Update Profile
```bash
curl -X PUT YOUR_SERVER/api/user-profiles/PROFILE_ID \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Updated",
    "email": "<EMAIL>"
  }'
```

#### Delete Profile
```bash
curl -X DELETE YOUR_SERVER/api/user-profiles/PROFILE_ID \
  -H "Authorization: Bearer $TOKEN"
```

### Authentication Endpoints

#### Get Current User
```bash
curl -H "Authorization: Bearer $TOKEN" \
  YOUR_SERVER/api/auth/me
```

#### Verify Token
```bash
curl -X POST YOUR_SERVER/api/auth/verify-token \
  -H "Authorization: Bearer $TOKEN"
```

#### Regenerate Token
```bash
curl -X POST YOUR_SERVER/api/auth/regenerate-token \
  -H "Authorization: Bearer $TOKEN"
```

#### Logout
```bash
curl -X POST YOUR_SERVER/api/auth/logout \
  -H "Authorization: Bearer $TOKEN"
```

### User Management (Super Admin Only)

#### Get All Users
```bash
curl -H "Authorization: Bearer $TOKEN" \
  YOUR_SERVER/api/users
```

#### Get User Stats
```bash
curl -H "Authorization: Bearer $TOKEN" \
  YOUR_SERVER/api/users/stats
```

#### Create User
```bash
curl -X POST YOUR_SERVER/api/users \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "password123",
    "role": "user"
  }'
```

---

## 💻 Code Snippets

### Python
```python
import requests

# Login
response = requests.post(
    "YOUR_SERVER/api/auth/login",
    json={"username": "admin", "password": "admin123"}
)
token = response.json()["data"]["token"]

# Use token
headers = {"Authorization": f"Bearer {token}"}
profiles = requests.get("YOUR_SERVER/api/user-profiles", headers=headers)
print(profiles.json())
```

### JavaScript
```javascript
// Login
const response = await fetch('YOUR_SERVER/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username: 'admin', password: 'admin123' })
});
const { data } = await response.json();
const token = data.token;

// Use token
const profiles = await fetch('YOUR_SERVER/api/user-profiles', {
    headers: { 'Authorization': `Bearer ${token}` }
});
console.log(await profiles.json());
```

### PHP
```php
// Login
$ch = curl_init('YOUR_SERVER/api/auth/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'username' => 'admin',
    'password' => 'admin123'
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = json_decode(curl_exec($ch), true);
$token = $response['data']['token'];

// Use token
$ch = curl_init('YOUR_SERVER/api/user-profiles');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Bearer ' . $token
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$profiles = json_decode(curl_exec($ch), true);
```

---

## 🔒 Response Formats

### Success Response
```json
{
  "success": true,
  "message": "Operation successful",
  "data": { ... },
  "count": 10  // For list endpoints
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message here"
}
```

---

## 🛠️ Testing Commands

### Test Server Health (No Auth)
```bash
curl YOUR_SERVER/api/health
```

### Test Full Flow
```bash
# 1. Login
TOKEN=$(curl -s -X POST YOUR_SERVER/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' \
  | grep -o '"token":"[^"]*' | cut -d'"' -f4)

# 2. Get profiles
curl -H "Authorization: Bearer $TOKEN" YOUR_SERVER/api/user-profiles

# 3. Get current user
curl -H "Authorization: Bearer $TOKEN" YOUR_SERVER/api/auth/me
```

---

## 🚨 Common Issues

| Issue | Solution |
|-------|----------|
| **Connection refused** | Check if server is running: `pm2 status` or `npm run server` |
| **401 Unauthorized** | Verify token format: `Authorization: Bearer TOKEN` |
| **CORS error** | Add your origin to CORS_ORIGIN in `.env` |
| **Can't connect from other device** | Check firewall: `sudo ufw allow 5000/tcp` |
| **Token invalid** | Get new token by logging in again |

---

## 📱 Mobile/External Access Checklist

- [ ] Find server IP: `hostname -I`
- [ ] Server is running: `pm2 status` or check terminal
- [ ] Firewall allows port 5000: `sudo ufw allow 5000/tcp`
- [ ] Test health endpoint: `curl http://YOUR_IP:5000/api/health`
- [ ] Get token: Login with curl from external device
- [ ] Save token securely
- [ ] Use token in all API calls

---

## 🌐 Production Deployment

### Quick Deploy to VPS
```bash
# On server
git clone YOUR_REPO
cd resilient-cape-web-admin-dashboard
npm install
pm2 start server/index.js --name "api"
pm2 save
pm2 startup
```

### Quick Deploy to Vercel
```bash
# Local machine
git push origin main
# Then import on vercel.com
```

---

## 📚 Full Documentation

- **Detailed Guide:** [EXTERNAL_API_ACCESS.md](./EXTERNAL_API_ACCESS.md)
- **API Documentation:** [API_USAGE_GUIDE.md](./API_USAGE_GUIDE.md)
- **Deployment:** [DEPLOYMENT.md](./DEPLOYMENT.md)
- **Main README:** [README.md](./README.md)

---

## 💡 Pro Tips

1. **Save your token** - It never expires, so save it securely
2. **Use environment variables** - Don't hardcode tokens in code
3. **Test locally first** - Use localhost before testing remotely
4. **Use HTTPS in production** - Never send tokens over HTTP in production
5. **Monitor logs** - Check `pm2 logs` for debugging

---

**Need help?** Check the full documentation or open an issue on GitHub.

