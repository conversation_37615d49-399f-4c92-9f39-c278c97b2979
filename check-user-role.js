#!/usr/bin/env node

require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./server/models/User');

async function checkUserRole() {
  try {
    // Connect to MongoDB using environment variable
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // List all users with their roles
    const users = await User.find().select('username email role isSuperAdmin isActive');
    console.log('\n👥 All users in the system:');
    console.log('=====================================');
    
    if (users.length === 0) {
      console.log('❌ No users found in the database!');
      console.log('\n💡 You need to create an admin user first.');
      console.log('Run: node create-admin.js');
    } else {
      users.forEach((user, index) => {
        console.log(`${index + 1}. Username: ${user.username}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Role: ${user.role}`);
        console.log(`   Super Admin: ${user.isSuperAdmin ? 'Yes' : 'No'}`);
        console.log(`   Active: ${user.isActive ? 'Yes' : 'No'}`);
        console.log('   ---');
      });
    }

    console.log('\n📋 Role Requirements for User Profiles:');
    console.log('- POST /api/user-profiles: Requires "admin" or "moderator" role');
    console.log('- PUT /api/user-profiles/:id: Requires "admin" or "moderator" role');
    console.log('- DELETE /api/user-profiles/:id: Requires "admin" role');
    console.log('- GET /api/user-profiles: Any authenticated user');

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

checkUserRole();
