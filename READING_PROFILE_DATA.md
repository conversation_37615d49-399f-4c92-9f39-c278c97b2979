# 📖 Reading Profile Data from API

## 📋 Profile Data Structure

Each profile contains:
- **name**: Full name (e.g., "<PERSON>")
- **jobTitle**: Job title (e.g., "Software Engineer")
- **image**: Base64-encoded image (can be displayed directly in HTML/apps)
- **_id**: Unique profile ID
- **createdAt**: When the profile was created

---

## 🔍 Example API Response

When you call the API, here's what you get:

```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:5000/api/user-profiles
```

**Response:**
```json
{
  "success": true,
  "count": 3,
  "data": [
    {
      "_id": "65a1b2c3d4e5f6789012345",
      "name": "<PERSON>",
      "jobTitle": "Software Engineer",
      "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBD...",
      "createdAt": "2025-01-15T10:30:00.000Z"
    },
    {
      "_id": "65a1b2c3d4e5f6789012346",
      "name": "<PERSON>",
      "jobTitle": "Product Designer",
      "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAA...",
      "createdAt": "2025-01-14T15:20:00.000Z"
    },
    {
      "_id": "65a1b2c3d4e5f6789012347",
      "name": "Mike Johnson",
      "jobTitle": "Marketing Manager",
      "image": "data:image/webp;base64,UklGRiQAAABXRUJQVlA4IBg...",
      "createdAt": "2025-01-13T09:45:00.000Z"
    }
  ]
}
```

---

## 💻 Reading Data in Different Languages

### 1. **Bash/Shell Script** (Pretty Print with jq)

```bash
# Get all profiles and format nicely
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/user-profiles | jq '.'

# Get just the names
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/user-profiles | jq '.data[].name'

# Get names and job titles
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/user-profiles | jq '.data[] | {name, jobTitle}'

# Count profiles
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/user-profiles | jq '.count'

# Save images to files
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/user-profiles | \
  jq -r '.data[] | .image' | \
  while read -r img; do
    # Extract base64 part and decode
    echo "$img" | sed 's/data:image\/[^;]*;base64,//' | base64 -d > "profile_$(date +%s).jpg"
  done
```

---

### 2. **Python** (Complete Example)

```python
import requests
import json
import base64
from datetime import datetime

class ProfileReader:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.token = token
        self.headers = {"Authorization": f"Bearer {token}"}
    
    def get_all_profiles(self):
        """Get all profiles"""
        response = requests.get(
            f"{self.base_url}/user-profiles",
            headers=self.headers
        )
        return response.json()
    
    def print_profiles(self):
        """Print all profile information"""
        data = self.get_all_profiles()
        
        if not data['success']:
            print(f"Error: {data.get('error')}")
            return
        
        print(f"\n📊 Found {data['count']} profiles:\n")
        print("=" * 80)
        
        for i, profile in enumerate(data['data'], 1):
            print(f"\n{i}. Profile ID: {profile['_id']}")
            print(f"   Name: {profile['name']}")
            print(f"   Job Title: {profile['jobTitle']}")
            print(f"   Created: {profile['createdAt']}")
            print(f"   Image: {profile['image'][:50]}... (base64)")
            print("-" * 80)
    
    def save_images(self, output_dir='./profile_images'):
        """Save all profile images to files"""
        import os
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        data = self.get_all_profiles()
        
        for profile in data['data']:
            # Extract base64 data
            image_data = profile['image']
            
            # Get image format (jpeg, png, webp)
            if 'jpeg' in image_data or 'jpg' in image_data:
                ext = 'jpg'
            elif 'png' in image_data:
                ext = 'png'
            elif 'webp' in image_data:
                ext = 'webp'
            else:
                ext = 'jpg'
            
            # Remove data URL prefix
            base64_str = image_data.split(',')[1]
            
            # Decode and save
            image_bytes = base64.b64decode(base64_str)
            filename = f"{output_dir}/{profile['name'].replace(' ', '_')}.{ext}"
            
            with open(filename, 'wb') as f:
                f.write(image_bytes)
            
            print(f"✅ Saved: {filename}")
    
    def get_profile_by_name(self, name):
        """Find profile by name"""
        data = self.get_all_profiles()
        
        for profile in data['data']:
            if name.lower() in profile['name'].lower():
                return profile
        
        return None
    
    def export_to_csv(self, filename='profiles.csv'):
        """Export profiles to CSV (without images)"""
        import csv
        
        data = self.get_all_profiles()
        
        with open(filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['ID', 'Name', 'Job Title', 'Created At'])
            writer.writeheader()
            
            for profile in data['data']:
                writer.writerow({
                    'ID': profile['_id'],
                    'Name': profile['name'],
                    'Job Title': profile['jobTitle'],
                    'Created At': profile['createdAt']
                })
        
        print(f"✅ Exported to {filename}")
    
    def export_to_json(self, filename='profiles.json'):
        """Export all data to JSON file"""
        data = self.get_all_profiles()
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Exported to {filename}")

# Usage Example
if __name__ == "__main__":
    # Initialize
    API_URL = "http://*************:5000/api"
    TOKEN = "your-token-here"
    
    reader = ProfileReader(API_URL, TOKEN)
    
    # Print all profiles
    reader.print_profiles()
    
    # Save images to files
    reader.save_images('./images')
    
    # Export to CSV
    reader.export_to_csv('team_profiles.csv')
    
    # Export to JSON
    reader.export_to_json('team_profiles.json')
    
    # Search for specific person
    profile = reader.get_profile_by_name("John")
    if profile:
        print(f"\n🔍 Found: {profile['name']} - {profile['jobTitle']}")
```

---

### 3. **JavaScript/Node.js** (Complete Example)

```javascript
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

class ProfileReader {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.token = token;
        this.headers = {
            'Authorization': `Bearer ${token}`
        };
    }

    async getAllProfiles() {
        const response = await fetch(`${this.baseUrl}/user-profiles`, {
            headers: this.headers
        });
        return response.json();
    }

    async printProfiles() {
        const data = await this.getAllProfiles();
        
        if (!data.success) {
            console.error(`Error: ${data.error}`);
            return;
        }

        console.log(`\n📊 Found ${data.count} profiles:\n`);
        console.log('='.repeat(80));

        data.data.forEach((profile, index) => {
            console.log(`\n${index + 1}. Profile ID: ${profile._id}`);
            console.log(`   Name: ${profile.name}`);
            console.log(`   Job Title: ${profile.jobTitle}`);
            console.log(`   Created: ${profile.createdAt}`);
            console.log(`   Image: ${profile.image.substring(0, 50)}... (base64)`);
            console.log('-'.repeat(80));
        });
    }

    async saveImages(outputDir = './profile_images') {
        // Create output directory
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }

        const data = await this.getAllProfiles();

        for (const profile of data.data) {
            // Extract image format
            const match = profile.image.match(/data:image\/(\w+);base64,/);
            const ext = match ? match[1] : 'jpg';

            // Remove data URL prefix
            const base64Data = profile.image.split(',')[1];

            // Decode and save
            const buffer = Buffer.from(base64Data, 'base64');
            const filename = path.join(outputDir, `${profile.name.replace(/\s+/g, '_')}.${ext}`);

            fs.writeFileSync(filename, buffer);
            console.log(`✅ Saved: ${filename}`);
        }
    }

    async exportToCSV(filename = 'profiles.csv') {
        const data = await this.getAllProfiles();

        const csvLines = ['ID,Name,Job Title,Created At'];

        data.data.forEach(profile => {
            csvLines.push(
                `"${profile._id}","${profile.name}","${profile.jobTitle}","${profile.createdAt}"`
            );
        });

        fs.writeFileSync(filename, csvLines.join('\n'));
        console.log(`✅ Exported to ${filename}`);
    }

    async exportToJSON(filename = 'profiles.json') {
        const data = await this.getAllProfiles();
        fs.writeFileSync(filename, JSON.stringify(data, null, 2));
        console.log(`✅ Exported to ${filename}`);
    }

    async getProfileByName(name) {
        const data = await this.getAllProfiles();
        return data.data.find(profile => 
            profile.name.toLowerCase().includes(name.toLowerCase())
        );
    }
}

// Usage Example
(async () => {
    const API_URL = 'http://*************:5000/api';
    const TOKEN = 'your-token-here';

    const reader = new ProfileReader(API_URL, TOKEN);

    // Print all profiles
    await reader.printProfiles();

    // Save images
    await reader.saveImages('./images');

    // Export to CSV
    await reader.exportToCSV('team_profiles.csv');

    // Export to JSON
    await reader.exportToJSON('team_profiles.json');

    // Search for specific person
    const profile = await reader.getProfileByName('John');
    if (profile) {
        console.log(`\n🔍 Found: ${profile.name} - ${profile.jobTitle}`);
    }
})();
```

---

### 4. **HTML/JavaScript** (Display in Web Page)

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team Profiles</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .profiles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .profile-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .profile-image {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 15px;
        }
        .profile-name {
            font-size: 1.5em;
            font-weight: bold;
            margin: 10px 0;
            color: #333;
        }
        .profile-title {
            color: #666;
            font-size: 1.1em;
            margin-bottom: 10px;
        }
        .profile-date {
            color: #999;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h1>Team Profiles</h1>
    <div id="profiles" class="profiles-grid"></div>

    <script>
        const API_URL = 'http://*************:5000/api';
        const TOKEN = 'your-token-here';

        async function loadProfiles() {
            try {
                const response = await fetch(`${API_URL}/user-profiles`, {
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    displayProfiles(data.data);
                } else {
                    console.error('Error:', data.error);
                }
            } catch (error) {
                console.error('Failed to load profiles:', error);
            }
        }

        function displayProfiles(profiles) {
            const container = document.getElementById('profiles');

            profiles.forEach(profile => {
                const card = document.createElement('div');
                card.className = 'profile-card';

                const date = new Date(profile.createdAt).toLocaleDateString();

                card.innerHTML = `
                    <img src="${profile.image}" alt="${profile.name}" class="profile-image">
                    <div class="profile-name">${profile.name}</div>
                    <div class="profile-title">${profile.jobTitle}</div>
                    <div class="profile-date">Joined: ${date}</div>
                `;

                container.appendChild(card);
            });
        }

        // Load profiles when page loads
        loadProfiles();
    </script>
</body>
</html>
```

---

## 🖼️ Working with Images

The images are stored as **base64-encoded data URLs**, which means:

### Display in HTML
```html
<img src="data:image/jpeg;base64,/9j/4AAQSkZJRg..." alt="Profile">
```

### Save to File (Python)
```python
import base64

# Extract base64 part
base64_str = image_data.split(',')[1]

# Decode and save
with open('profile.jpg', 'wb') as f:
    f.write(base64.b64decode(base64_str))
```

### Save to File (Node.js)
```javascript
const fs = require('fs');

// Extract base64 part
const base64Data = imageData.split(',')[1];

// Decode and save
const buffer = Buffer.from(base64Data, 'base64');
fs.writeFileSync('profile.jpg', buffer);
```

---

## 📊 Quick Commands

```bash
# Get all profiles (formatted)
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/user-profiles | jq '.'

# Get just names and titles
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/user-profiles | \
  jq '.data[] | "\(.name) - \(.jobTitle)"'

# Count profiles
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/user-profiles | jq '.count'

# Get single profile by ID
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/user-profiles/PROFILE_ID | jq '.'

# Save response to file
curl -s -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/user-profiles > profiles.json
```

---

## 🎯 Summary

When you call the API, you get:
- ✅ **Complete profile data** (name, job title, image, dates)
- ✅ **Images as base64** (ready to display or save)
- ✅ **JSON format** (easy to parse in any language)
- ✅ **All profiles at once** or individual profiles by ID

The base64 images can be:
- Displayed directly in HTML/apps
- Saved to files
- Processed/resized
- Sent to other services

See the code examples above for complete implementations!

