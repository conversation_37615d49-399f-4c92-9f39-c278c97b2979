{"name": "resilient-cape-admin-dashboard", "version": "1.0.0", "description": "Secure admin dashboard with JWT authentication, Bearer token API access, and user profile management for Resilient Cape", "main": "server/index.js", "scripts": {"setup": "node setup.js", "get-token": "node get-token.js", "get-token-simple": "node get-token-simple.js", "test-api": "node test-api.js", "test-external": "node test-external-api.js", "view-profiles": "node view-profiles.js", "dev": "concurrently \"npm run server\" \"npm run client\"", "server": "nodemon server/index.js", "client": "cd client && python3 -m http.server 8080", "build": "cd client && npm run build", "install-all": "npm install && cd client && npm install", "start": "node server/index.js"}, "dependencies": {"@vendia/serverless-express": "^4.12.6", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.1", "node-fetch": "^2.7.0"}, "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^3.0.1"}}