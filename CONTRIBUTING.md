# Contributing to Resilient Cape Admin Dashboard

Thank you for your interest in contributing to the Resilient Cape Admin Dashboard! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### 1. <PERSON> and <PERSON>lone
```bash
# Fork the repository on GitHub
# Then clone your fork
git clone https://github.com/oni1997/resilient-cape-web-admin-dashboard.git
cd resilient-cape-web-admin-dashboard
```

### 2. Set Up Development Environment
```bash
# Install dependencies
npm install

# Copy environment template
cp .env.example .env

# Start development servers
npm run server  # Backend on port 5000
# In another terminal:
cd client && python3 -m http.server 8080  # Frontend on port 8080
```

### 3. Create a Feature Branch
```bash
git checkout -b feature/your-feature-name
# or
git checkout -b bugfix/issue-description
```

### 4. Make Your Changes
- Follow the coding standards below
- Add tests if applicable
- Update documentation if needed

### 5. Test Your Changes
```bash
# Test authentication flow
node test-api.js

# Test the application manually
# Visit http://localhost:8080/login.html
```

### 6. Comm<PERSON> and Push
```bash
git add .
git commit -m "feat: add new feature description"
git push origin feature/your-feature-name
```

### 7. Create Pull Request
- Go to GitHub and create a pull request
- Provide clear description of changes
- Reference any related issues

## 📝 Coding Standards

### JavaScript Style Guide

- Use **ES6+** features where appropriate
- Use **const** for constants, **let** for variables
- Use **async/await** instead of promises where possible
- Add **JSDoc comments** for functions
- Use **camelCase** for variables and functions
- Use **PascalCase** for classes and constructors

### Example Code Style

```javascript
/**
 * Authenticate user and return JWT token
 * @param {string} username - User's username or email
 * @param {string} password - User's password
 * @returns {Promise<Object>} Authentication response with token
 */
async function authenticateUser(username, password) {
  try {
    const user = await User.findOne({
      $or: [{ username }, { email: username }],
      isActive: true
    });
    
    if (!user || !await user.comparePassword(password)) {
      throw new Error('Invalid credentials');
    }
    
    return generateToken(user._id);
  } catch (error) {
    console.error('Authentication error:', error);
    throw error;
  }
}
```

### File Organization

- **Models**: Database schemas in `server/models/`
- **Routes**: API endpoints in `server/routes/`
- **Middleware**: Express middleware in `server/middleware/`
- **Frontend**: Client files in `client/`
- **Config**: Configuration files in `server/config/`

## 🧪 Testing Guidelines

### Manual Testing Checklist

Before submitting a PR, test these scenarios:

#### Authentication Flow
- [ ] User registration works
- [ ] User login works
- [ ] Token verification works
- [ ] Logout clears session
- [ ] Invalid credentials are rejected
- [ ] Expired tokens are handled

#### User Profile Management
- [ ] Create profile with image upload
- [ ] View all profiles
- [ ] View single profile details
- [ ] Update existing profile
- [ ] Delete profile (admin only)

#### Security Testing
- [ ] Unauthenticated requests are blocked
- [ ] Invalid tokens are rejected
- [ ] Role-based permissions work
- [ ] CORS is properly configured

### API Testing

Use the provided test script:
```bash
node test-api.js
```

Or test manually with curl:
```bash
# Login and get token
TOKEN=$(curl -s -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' | \
  jq -r '.data.token')

# Use token to access protected route
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:5000/api/user-profiles
```

## 🐛 Bug Reports

When reporting bugs, please include:

1. **Environment Information:**
   - Node.js version
   - MongoDB version
   - Operating system
   - Browser (for frontend issues)

2. **Steps to Reproduce:**
   - Clear step-by-step instructions
   - Expected vs actual behavior
   - Screenshots if applicable

3. **Error Messages:**
   - Console errors
   - Server logs
   - Network errors

### Bug Report Template

```markdown
**Environment:**
- Node.js: v18.17.0
- MongoDB: v6.0.8
- OS: Ubuntu 22.04
- Browser: Chrome 115.0

**Steps to Reproduce:**
1. Navigate to login page
2. Enter credentials
3. Click login button

**Expected Behavior:**
User should be redirected to dashboard

**Actual Behavior:**
Error message appears: "Invalid token"

**Error Messages:**
Console: TypeError: Cannot read property 'token' of undefined
```

## 🚀 Feature Requests

When requesting features, please include:

1. **Use Case:** Why is this feature needed?
2. **Description:** What should the feature do?
3. **Implementation Ideas:** Any thoughts on how to implement?
4. **Priority:** How important is this feature?

## 🔒 Security Considerations

When contributing, keep security in mind:

- **Never commit sensitive data** (passwords, tokens, API keys)
- **Validate all inputs** on both client and server side
- **Use parameterized queries** to prevent injection attacks
- **Follow authentication best practices**
- **Test authorization thoroughly**

### Security Checklist

- [ ] Input validation implemented
- [ ] SQL/NoSQL injection prevention
- [ ] XSS protection in place
- [ ] CSRF protection considered
- [ ] Sensitive data not logged
- [ ] Error messages don't leak information

## 📚 Development Resources

### Useful Documentation
- [Express.js Documentation](https://expressjs.com/)
- [MongoDB Documentation](https://docs.mongodb.com/)
- [Mongoose Documentation](https://mongoosejs.com/)
- [JWT Documentation](https://jwt.io/)
- [bcrypt Documentation](https://github.com/kelektiv/node.bcrypt.js)

### Development Tools
- **Postman**: For API testing
- **MongoDB Compass**: For database management
- **VS Code**: Recommended editor with extensions:
  - ES6 String HTML
  - MongoDB for VS Code
  - REST Client

## 🎯 Contribution Areas

We welcome contributions in these areas:

### High Priority
- [ ] Unit tests for authentication
- [ ] Integration tests for API endpoints
- [ ] Input sanitization improvements
- [ ] Rate limiting implementation
- [ ] Password reset functionality

### Medium Priority
- [ ] User management interface
- [ ] Audit logging
- [ ] Email notifications
- [ ] Profile picture optimization
- [ ] Bulk operations

### Low Priority
- [ ] Dark mode theme
- [ ] Export functionality
- [ ] Advanced search/filtering
- [ ] Mobile app
- [ ] Internationalization

## 📞 Getting Help

If you need help:

1. **Check existing issues** on GitHub
2. **Read the documentation** in README.md
3. **Ask questions** in GitHub Discussions
4. **Join our community** (if applicable)

## 🏆 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Project documentation

Thank you for contributing to Resilient Cape Admin Dashboard! 🎉
