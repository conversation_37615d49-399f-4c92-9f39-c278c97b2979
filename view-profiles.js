#!/usr/bin/env node

/**
 * Profile Viewer Script
 * View and export profile data from the API
 * 
 * Usage:
 *   node view-profiles.js
 *   node view-profiles.js --export-images
 *   node view-profiles.js --export-csv
 *   node view-profiles.js --export-json
 */

const readline = require('readline');
const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Colors for terminal output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function viewProfiles() {
  try {
    log('\n📊 Profile Viewer', 'bright');
    log('=' .repeat(80), 'cyan');
    
    // Get server URL
    const defaultUrl = process.argv[2] || 'http://localhost:5000';
    log(`\n📍 Default server: ${defaultUrl}`, 'blue');
    const serverUrl = await question(`Enter server URL (press Enter for default): `) || defaultUrl;
    const baseUrl = serverUrl.replace(/\/$/, '');
    
    // Get token
    log('\n🔑 Authentication', 'bright');
    const hasToken = await question('Do you have a token? (y/n): ');
    
    let token;
    if (hasToken.toLowerCase() === 'y' || hasToken.toLowerCase() === 'yes') {
      token = await question('Enter your token: ');
    } else {
      log('\n📝 Login to get token', 'yellow');
      const username = await question('Username: ');
      const password = await question('Password: ');
      
      try {
        const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ username, password })
        });
        
        const loginData = await loginResponse.json();
        
        if (loginData.success) {
          token = loginData.data.token;
          log('✅ Login successful!', 'green');
        } else {
          log(`❌ Login failed: ${loginData.error}`, 'red');
          rl.close();
          return;
        }
      } catch (error) {
        log(`❌ Login error: ${error.message}`, 'red');
        rl.close();
        return;
      }
    }
    
    // Fetch profiles
    log('\n📥 Fetching profiles...', 'yellow');
    
    try {
      const response = await fetch(`${baseUrl}/api/user-profiles`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      const data = await response.json();
      
      if (!data.success) {
        log(`❌ Error: ${data.error}`, 'red');
        rl.close();
        return;
      }
      
      // Display profiles
      log('\n' + '='.repeat(80), 'cyan');
      log(`📊 Found ${data.count} Profile(s)`, 'bright');
      log('='.repeat(80), 'cyan');
      
      if (data.count === 0) {
        log('\n⚠️  No profiles found. Create some profiles first!', 'yellow');
        rl.close();
        return;
      }
      
      data.data.forEach((profile, index) => {
        log(`\n${index + 1}. ${profile.name}`, 'bright');
        log(`   📋 ID: ${profile._id}`, 'blue');
        log(`   💼 Job Title: ${profile.jobTitle}`, 'cyan');
        log(`   📅 Created: ${new Date(profile.createdAt).toLocaleString()}`, 'magenta');
        
        // Show image info
        const imageSize = profile.image.length;
        const imageSizeKB = (imageSize / 1024).toFixed(2);
        const imageType = profile.image.match(/data:image\/(\w+);/)?.[1] || 'unknown';
        log(`   🖼️  Image: ${imageType.toUpperCase()} (${imageSizeKB} KB)`, 'green');
        log(`   📏 Base64 length: ${imageSize} characters`, 'blue');
        
        log('-'.repeat(80), 'cyan');
      });
      
      // Export options
      log('\n📤 Export Options', 'bright');
      log('='.repeat(80), 'cyan');
      
      const exportChoice = await question(
        '\nWhat would you like to export?\n' +
        '  1. Images (save all profile images to files)\n' +
        '  2. CSV (names and job titles)\n' +
        '  3. JSON (complete data)\n' +
        '  4. All of the above\n' +
        '  5. Nothing (exit)\n' +
        'Enter choice (1-5): '
      );
      
      const choice = parseInt(exportChoice);
      
      if (choice === 1 || choice === 4) {
        await exportImages(data.data);
      }
      
      if (choice === 2 || choice === 4) {
        await exportCSV(data.data);
      }
      
      if (choice === 3 || choice === 4) {
        await exportJSON(data);
      }
      
      if (choice === 5) {
        log('\n👋 Goodbye!', 'green');
      }
      
      // Show summary
      log('\n' + '='.repeat(80), 'cyan');
      log('✅ Done!', 'bright');
      log('='.repeat(80), 'cyan');
      
    } catch (error) {
      log(`❌ Error fetching profiles: ${error.message}`, 'red');
    }
    
  } catch (error) {
    log(`\n❌ Unexpected error: ${error.message}`, 'red');
    console.error(error);
  } finally {
    rl.close();
  }
}

async function exportImages(profiles) {
  const outputDir = './profile_images';
  
  // Create directory
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  log(`\n📁 Saving images to: ${outputDir}`, 'yellow');
  
  profiles.forEach((profile, index) => {
    try {
      // Extract image format
      const match = profile.image.match(/data:image\/(\w+);base64,/);
      const ext = match ? match[1] : 'jpg';
      
      // Remove data URL prefix
      const base64Data = profile.image.split(',')[1];
      
      // Decode and save
      const buffer = Buffer.from(base64Data, 'base64');
      const safeName = profile.name.replace(/[^a-zA-Z0-9]/g, '_');
      const filename = path.join(outputDir, `${index + 1}_${safeName}.${ext}`);
      
      fs.writeFileSync(filename, buffer);
      log(`  ✅ Saved: ${filename}`, 'green');
    } catch (error) {
      log(`  ❌ Failed to save image for ${profile.name}: ${error.message}`, 'red');
    }
  });
}

async function exportCSV(profiles) {
  const filename = 'profiles.csv';
  
  log(`\n📄 Exporting to CSV: ${filename}`, 'yellow');
  
  try {
    const lines = ['ID,Name,Job Title,Created At'];
    
    profiles.forEach(profile => {
      const date = new Date(profile.createdAt).toISOString();
      lines.push(`"${profile._id}","${profile.name}","${profile.jobTitle}","${date}"`);
    });
    
    fs.writeFileSync(filename, lines.join('\n'));
    log(`  ✅ Exported ${profiles.length} profiles to ${filename}`, 'green');
  } catch (error) {
    log(`  ❌ Failed to export CSV: ${error.message}`, 'red');
  }
}

async function exportJSON(data) {
  const filename = 'profiles.json';
  
  log(`\n📄 Exporting to JSON: ${filename}`, 'yellow');
  
  try {
    fs.writeFileSync(filename, JSON.stringify(data, null, 2));
    log(`  ✅ Exported complete data to ${filename}`, 'green');
  } catch (error) {
    log(`  ❌ Failed to export JSON: ${error.message}`, 'red');
  }
}

// Run the viewer
viewProfiles().catch(error => {
  console.error('Fatal error:', error);
  rl.close();
  process.exit(1);
});

