#!/usr/bin/env node

/**
 * External API Test Script
 * Tests API access from any system (local network, remote server, etc.)
 * 
 * Usage:
 *   node test-external-api.js
 *   node test-external-api.js http://*************:5000
 *   node test-external-api.js https://your-app.vercel.app
 */

const readline = require('readline');
const fetch = require('node-fetch');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Colors for terminal output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function testAPI() {
  try {
    log('\n🌐 External API Access Test', 'bright');
    log('=' .repeat(50), 'cyan');
    
    // Get server URL
    const defaultUrl = process.argv[2] || 'http://localhost:5000';
    log(`\n📍 Default server: ${defaultUrl}`, 'blue');
    const serverUrl = await question(`Enter server URL (press Enter for default): `) || defaultUrl;
    const baseUrl = serverUrl.replace(/\/$/, ''); // Remove trailing slash
    
    log(`\n✅ Using server: ${baseUrl}`, 'green');
    
    // Test 1: Health Check
    log('\n' + '='.repeat(50), 'cyan');
    log('Test 1: Health Check (No Authentication)', 'bright');
    log('='.repeat(50), 'cyan');
    
    try {
      const healthResponse = await fetch(`${baseUrl}/api/health`);
      const healthData = await healthResponse.json();
      
      if (healthData.success) {
        log('✅ Server is running!', 'green');
        log(`   Message: ${healthData.message}`, 'blue');
        log(`   Timestamp: ${healthData.timestamp}`, 'blue');
      } else {
        log('❌ Server responded but health check failed', 'red');
        console.log(healthData);
      }
    } catch (error) {
      log('❌ Cannot connect to server', 'red');
      log(`   Error: ${error.message}`, 'red');
      log('\n💡 Troubleshooting:', 'yellow');
      log('   1. Check if server is running', 'yellow');
      log('   2. Verify the URL is correct', 'yellow');
      log('   3. Check firewall settings', 'yellow');
      log('   4. Ensure port 5000 is accessible', 'yellow');
      rl.close();
      return;
    }
    
    // Test 2: Login
    log('\n' + '='.repeat(50), 'cyan');
    log('Test 2: Login and Get Token', 'bright');
    log('='.repeat(50), 'cyan');
    
    const username = await question('Enter username (default: admin): ') || 'admin';
    const password = await question('Enter password (default: admin123): ') || 'admin123';
    
    let token;
    try {
      const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username, password })
      });
      
      const loginData = await loginResponse.json();
      
      if (loginData.success) {
        token = loginData.data.token;
        log('✅ Login successful!', 'green');
        log(`   Username: ${loginData.data.user.username}`, 'blue');
        log(`   Email: ${loginData.data.user.email}`, 'blue');
        log(`   Role: ${loginData.data.user.role}`, 'blue');
        log(`\n🔑 Token (first 50 chars): ${token.substring(0, 50)}...`, 'cyan');
      } else {
        log('❌ Login failed', 'red');
        log(`   Error: ${loginData.error}`, 'red');
        rl.close();
        return;
      }
    } catch (error) {
      log('❌ Login request failed', 'red');
      log(`   Error: ${error.message}`, 'red');
      rl.close();
      return;
    }
    
    // Test 3: Get Current User
    log('\n' + '='.repeat(50), 'cyan');
    log('Test 3: Get Current User Info (Authenticated)', 'bright');
    log('='.repeat(50), 'cyan');
    
    try {
      const meResponse = await fetch(`${baseUrl}/api/auth/me`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const meData = await meResponse.json();
      
      if (meData.success) {
        log('✅ Authentication working!', 'green');
        log(`   User ID: ${meData.data.user._id}`, 'blue');
        log(`   Username: ${meData.data.user.username}`, 'blue');
        log(`   Email: ${meData.data.user.email}`, 'blue');
        log(`   Role: ${meData.data.user.role}`, 'blue');
        log(`   Active: ${meData.data.user.isActive}`, 'blue');
      } else {
        log('❌ Failed to get user info', 'red');
        log(`   Error: ${meData.error}`, 'red');
      }
    } catch (error) {
      log('❌ Request failed', 'red');
      log(`   Error: ${error.message}`, 'red');
    }
    
    // Test 4: Get User Profiles
    log('\n' + '='.repeat(50), 'cyan');
    log('Test 4: Get User Profiles (Authenticated)', 'bright');
    log('='.repeat(50), 'cyan');
    
    try {
      const profilesResponse = await fetch(`${baseUrl}/api/user-profiles`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const profilesData = await profilesResponse.json();
      
      if (profilesData.success) {
        log(`✅ Retrieved ${profilesData.count} profiles`, 'green');
        
        if (profilesData.count > 0) {
          log('\n   Sample profiles:', 'blue');
          profilesData.data.slice(0, 3).forEach((profile, index) => {
            log(`   ${index + 1}. ${profile.name} (${profile.email})`, 'cyan');
          });
          
          if (profilesData.count > 3) {
            log(`   ... and ${profilesData.count - 3} more`, 'cyan');
          }
        } else {
          log('   No profiles found. You can create one through the dashboard.', 'yellow');
        }
      } else {
        log('❌ Failed to get profiles', 'red');
        log(`   Error: ${profilesData.error}`, 'red');
      }
    } catch (error) {
      log('❌ Request failed', 'red');
      log(`   Error: ${error.message}`, 'red');
    }
    
    // Test 5: Verify Token
    log('\n' + '='.repeat(50), 'cyan');
    log('Test 5: Verify Token', 'bright');
    log('='.repeat(50), 'cyan');
    
    try {
      const verifyResponse = await fetch(`${baseUrl}/api/auth/verify-token`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const verifyData = await verifyResponse.json();
      
      if (verifyData.success) {
        log('✅ Token is valid!', 'green');
        log(`   Message: ${verifyData.message}`, 'blue');
      } else {
        log('❌ Token verification failed', 'red');
        log(`   Error: ${verifyData.error}`, 'red');
      }
    } catch (error) {
      log('❌ Request failed', 'red');
      log(`   Error: ${error.message}`, 'red');
    }
    
    // Summary
    log('\n' + '='.repeat(50), 'cyan');
    log('📊 Test Summary', 'bright');
    log('='.repeat(50), 'cyan');
    log('✅ All tests completed!', 'green');
    log('\n💡 Next Steps:', 'yellow');
    log('   1. Save your token for future use', 'yellow');
    log('   2. Use this token in your applications', 'yellow');
    log('   3. See EXTERNAL_API_ACCESS.md for code examples', 'yellow');
    log('   4. See QUICK_API_REFERENCE.md for API endpoints', 'yellow');
    
    log('\n🔑 Your Bearer Token:', 'bright');
    log(`   ${token}`, 'cyan');
    
    log('\n📋 Example curl command:', 'bright');
    log(`   curl -H "Authorization: Bearer ${token}" \\`, 'cyan');
    log(`     ${baseUrl}/api/user-profiles`, 'cyan');
    
    log('\n📋 Example Python code:', 'bright');
    log(`   import requests`, 'cyan');
    log(`   headers = {"Authorization": "Bearer ${token.substring(0, 30)}..."}`, 'cyan');
    log(`   response = requests.get("${baseUrl}/api/user-profiles", headers=headers)`, 'cyan');
    log(`   print(response.json())`, 'cyan');
    
    // Ask if user wants to save token
    log('\n' + '='.repeat(50), 'cyan');
    const saveToken = await question('Would you like to save the token to a file? (y/n): ');
    
    if (saveToken.toLowerCase() === 'y' || saveToken.toLowerCase() === 'yes') {
      const fs = require('fs');
      const tokenData = {
        server: baseUrl,
        username: username,
        token: token,
        generatedAt: new Date().toISOString(),
        expiresAt: 'Never (permanent token)',
        note: 'Keep this token secure. Do not commit to version control.'
      };
      
      fs.writeFileSync('api-token.json', JSON.stringify(tokenData, null, 2));
      log('✅ Token saved to api-token.json', 'green');
      log('⚠️  Remember to add api-token.json to .gitignore!', 'yellow');
    }
    
  } catch (error) {
    log('\n❌ Unexpected error:', 'red');
    console.error(error);
  } finally {
    rl.close();
  }
}

// Run the test
testAPI().catch(error => {
  console.error('Fatal error:', error);
  rl.close();
  process.exit(1);
});

