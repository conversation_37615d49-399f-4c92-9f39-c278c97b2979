/* Modern Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Modern Color Palette */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #00d4aa;
    --warning-color: #ff6b6b;
    --danger-color: #ff4757;
    
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-light: #bdc3c7;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    
    --border-color: #e1e8ed;
    --border-radius: 12px;
    --border-radius-lg: 20px;
    
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.1);
    
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 2rem;
}

/* Modern Header */
header {
    background: var(--primary-gradient);
    color: white;
    padding: 3rem 2rem;
    border-radius: var(--border-radius-lg);
    margin-bottom: 3rem;
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

header h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
}

/* Modern Section Styles */
section {
    background: var(--bg-primary);
    padding: 2.5rem;
    margin-bottom: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

section:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

section h2 {
    color: var(--text-primary);
    margin-bottom: 2rem;
    font-size: 1.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-description {
    color: var(--text-secondary);
    font-style: italic;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    padding: 2rem;
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stat-card h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-card p {
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

/* Modern Bearer Token Section */
.token-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
}

.token-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    pointer-events: none;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
    z-index: 1;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-header p {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

.token-main-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    position: relative;
    z-index: 1;
}

.token-display-card {
    background: rgba(255, 255, 255, 0.15);
    border-radius: var(--border-radius-lg);
    padding: 3rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.token-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.token-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
}

.token-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #00ff88;
    box-shadow: 0 0 10px #00ff88;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.token-field {
    margin-bottom: 2rem;
}

.token-field input {
    width: 100%;
    padding: 1.5rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.95);
    color: var(--text-primary);
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 1rem;
    font-weight: 500;
    transition: var(--transition);
}

.token-field input:focus {
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
}

.token-actions {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.btn-icon {
    font-size: 1.5rem;
}

.btn-text {
    font-weight: 600;
    font-size: 0.9rem;
}

.copy-btn:hover {
    border-color: #00ff88;
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
}

.regenerate-btn:hover {
    border-color: #ff6b6b;
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
}

.examples-btn:hover {
    border-color: #4ecdc4;
    box-shadow: 0 0 20px rgba(78, 205, 196, 0.3);
}

.token-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.info-label {
    font-weight: 600;
    opacity: 0.8;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-weight: 500;
    font-size: 1rem;
}

.usage-code {
    background: rgba(0, 0, 0, 0.3);
    padding: 0.75rem;
    border-radius: 6px;
    font-family: 'SF Mono', 'Monaco', monospace;
    font-size: 0.85rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Quick Actions Panel */
.api-quick-actions {
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.api-quick-actions h3 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.3rem;
    font-weight: 600;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 2rem 1.5rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    text-align: center;
}

.quick-action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-6px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.action-icon {
    font-size: 2rem;
}

.action-title {
    font-weight: 700;
    font-size: 1rem;
}

.action-desc {
    font-size: 0.85rem;
    opacity: 0.8;
}

/* Examples Section */
.examples-section {
    margin-top: 3rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.examples-section h3 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.4rem;
    font-weight: 600;
}

.examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.example-card {
    background: rgba(0, 0, 0, 0.3);
    border-radius: var(--border-radius);
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
}

.example-card h4 {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: #00ff88;
}

.example-card pre {
    background: rgba(0, 0, 0, 0.5);
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    margin-bottom: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.example-card code {
    color: #f8f8f2;
    font-family: 'SF Mono', 'Monaco', monospace;
    font-size: 0.9rem;
    line-height: 1.6;
}

.copy-example-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    color: white;
    font-size: 0.8rem;
    cursor: pointer;
    transition: var(--transition);
}

.copy-example-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Modern Form Styles */
.form-group {
    margin-bottom: 2rem;
}

label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
}

input[type="text"],
input[type="file"],
select {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--bg-primary);
    color: var(--text-primary);
}

input[type="text"]:focus,
input[type="file"]:focus,
select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

/* Modern Button Styles */
button {
    background: var(--primary-gradient);
    color: white;
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

button:hover::before {
    left: 100%;
}

button:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

button:active {
    transform: translateY(-1px);
}

button:disabled {
    background: var(--text-light);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Specialized Button Styles */
.btn-small {
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
}

.btn-danger {
    background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
}

.btn-success {
    background: linear-gradient(135deg, #00d4aa 0%, #01a3a4 100%);
}

.btn-warning {
    background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
}

/* Modern Grid Layouts */
.profiles-grid,
.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
}

/* Enhanced Card Styles */
.profile-card,
.user-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.profile-card::before,
.user-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.profile-card:hover::before,
.user-card:hover::before {
    transform: scaleX(1);
}

.profile-card:hover,
.user-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.profile-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
    transition: var(--transition);
}

.profile-card:hover .profile-image {
    transform: scale(1.02);
}

.profile-name,
.user-name {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
}

.profile-job {
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.profile-date {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

.profile-actions,
.user-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    margin-top: 1.5rem;
}

/* User Management Specific Styles */
.user-card.inactive {
    opacity: 0.7;
    border-color: var(--warning-color);
}

.user-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.user-role {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.user-role.super_admin {
    background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
    color: white;
}

.user-role.admin {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.user-role.moderator {
    background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
    color: white;
}

.user-role.user {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    color: white;
}

.user-details p {
    margin-bottom: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.status.active {
    color: var(--success-color);
    font-weight: 700;
}

.status.inactive {
    color: var(--danger-color);
    font-weight: 700;
}

.super-admin-badge {
    background: var(--secondary-gradient);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Form Row Layout */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

/* Controls Section */
.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.download-btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.2);
}

.download-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea085);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.download-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Modal Enhancements */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--bg-primary);
    margin: 5% auto;
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-xl);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.close {
    color: var(--text-light);
    float: right;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
}

.close:hover {
    color: var(--danger-color);
    transform: scale(1.1);
}

.modal-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    justify-content: flex-end;
}

/* Message Styles */
.loading {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.error,
.error-message {
    background: linear-gradient(135deg, #fee 0%, #fdd 100%);
    color: var(--danger-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin: 1rem 0;
    border-left: 4px solid var(--danger-color);
    font-weight: 500;
}

.success,
.success-message {
    background: linear-gradient(135deg, #efe 0%, #dfd 100%);
    color: var(--success-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin: 1rem 0;
    border-left: 4px solid var(--success-color);
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .container {
        padding: 1.5rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 1024px) {
    .token-main-container {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .examples-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    header {
        padding: 2rem 1.5rem;
    }

    header h1 {
        font-size: 2rem;
    }

    section {
        padding: 1.5rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .section-header p {
        font-size: 1rem;
    }

    .token-display-card {
        padding: 2rem;
    }

    .token-actions {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .token-info-grid {
        grid-template-columns: 1fr;
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
    }

    .profiles-grid,
    .users-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .examples-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .modal-content {
        margin: 10% auto;
        padding: 2rem;
    }

    .controls {
        flex-direction: column;
        gap: 1rem;
    }
}
