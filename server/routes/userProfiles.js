const express = require('express');
const router = express.Router();
const UserProfile = require('../models/UserProfile');
const multer = require('multer');
const { auth, authorize } = require('../middleware/auth');

// Configure multer for memory storage (we'll process the file in memory)
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'image/jpeg' || file.mimetype === 'image/jpg' || file.mimetype === 'image/png' || file.mimetype === 'image/webp') {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPG, PNG, and WebP images are allowed.'));
    }
  }
});

// GET all user profiles - Protected route
router.get('/', auth, async (req, res) => {
  try {
    const profiles = await UserProfile.find().sort({ createdAt: -1 });
    res.json({
      success: true,
      count: profiles.length,
      data: profiles
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + error.message
    });
  }
});

// GET single user profile - Protected route
router.get('/:id', auth, async (req, res) => {
  try {
    const profile = await UserProfile.findById(req.params.id);
    
    if (!profile) {
      return res.status(404).json({
        success: false,
        error: 'Profile not found'
      });
    }
    
    res.json({
      success: true,
      data: profile
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + error.message
    });
  }
});

// POST new user profile - Protected route (admin only)
router.post('/', auth, authorize('admin', 'moderator'), upload.single('image'), async (req, res) => {
  try {
    const { name, jobTitle } = req.body;

    if (!name || !jobTitle) {
      return res.status(400).json({
        success: false,
        error: 'Please provide name and job title'
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'Please upload an image'
      });
    }

    // Convert image to base64
    const base64Image = `data:${req.file.mimetype};base64,${req.file.buffer.toString('base64')}`;

    const profile = await UserProfile.create({
      name,
      jobTitle,
      image: base64Image
    });

    res.status(201).json({
      success: true,
      data: profile,
      message: 'Profile created successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + error.message
    });
  }
});

// PUT update user profile - Protected route (admin only)
router.put('/:id', auth, authorize('admin', 'moderator'), upload.single('image'), async (req, res) => {
  try {
    const { name, jobTitle } = req.body;
    let updateData = { name, jobTitle };

    if (req.file) {
      const base64Image = `data:${req.file.mimetype};base64,${req.file.buffer.toString('base64')}`;
      updateData.image = base64Image;
    }

    const profile = await UserProfile.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    );

    if (!profile) {
      return res.status(404).json({
        success: false,
        error: 'Profile not found'
      });
    }

    res.json({
      success: true,
      data: profile,
      message: 'Profile updated successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + error.message
    });
  }
});

// DELETE user profile - Protected route (admin only)
router.delete('/:id', auth, authorize('admin'), async (req, res) => {
  try {
    const profile = await UserProfile.findByIdAndDelete(req.params.id);

    if (!profile) {
      return res.status(404).json({
        success: false,
        error: 'Profile not found'
      });
    }

    res.json({
      success: true,
      message: 'Profile deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + error.message
    });
  }
});

module.exports = router;
