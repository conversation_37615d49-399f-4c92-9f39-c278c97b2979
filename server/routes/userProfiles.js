
const express = require('express');
const router = express.Router();
const UserProfile = require('../models/UserProfile');
const multer = require('multer');
const { auth, authorize } = require('../middleware/auth');
const archiver = require('archiver');
const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

// Configure multer for memory storage (we'll process the file in memory)
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'image/jpeg' || file.mimetype === 'image/jpg' || file.mimetype === 'image/png' || file.mimetype === 'image/webp') {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPG, PNG, and WebP images are allowed.'));
    }
  }
});

// GET all user profiles - Protected route
router.get('/', auth, async (req, res) => {
  try {
    const profiles = await UserProfile.find().sort({ createdAt: -1 });
    res.json({
      success: true,
      count: profiles.length,
      data: profiles
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + error.message
    });
  }
});

// GET single user profile - Protected route
router.get('/:id', auth, async (req, res) => {
  try {
    const profile = await UserProfile.findById(req.params.id);
    
    if (!profile) {
      return res.status(404).json({
        success: false,
        error: 'Profile not found'
      });
    }
    
    res.json({
      success: true,
      data: profile
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + error.message
    });
  }
});

// POST new user profile - Protected route (admin only)
router.post('/', auth, authorize('super_admin', 'admin', 'moderator'), upload.single('image'), async (req, res) => {
  try {
    const { name, jobTitle } = req.body;

    if (!name || !jobTitle) {
      return res.status(400).json({
        success: false,
        error: 'Please provide name and job title'
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'Please upload an image'
      });
    }

    // Convert image to base64
    const base64Image = `data:${req.file.mimetype};base64,${req.file.buffer.toString('base64')}`;

    const profile = await UserProfile.create({
      name,
      jobTitle,
      image: base64Image
    });

    res.status(201).json({
      success: true,
      data: profile,
      message: 'Profile created successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + error.message
    });
  }
});

// PUT update user profile - Protected route (admin only)
router.put('/:id', auth, authorize('super_admin', 'admin', 'moderator'), upload.single('image'), async (req, res) => {
  try {
    const { name, jobTitle } = req.body;
    let updateData = { name, jobTitle };

    if (req.file) {
      const base64Image = `data:${req.file.mimetype};base64,${req.file.buffer.toString('base64')}`;
      updateData.image = base64Image;
    }

    const profile = await UserProfile.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    );

    if (!profile) {
      return res.status(404).json({
        success: false,
        error: 'Profile not found'
      });
    }

    res.json({
      success: true,
      data: profile,
      message: 'Profile updated successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + error.message
    });
  }
});

// DELETE user profile - Protected route (admin only)
router.delete('/:id', auth, authorize('super_admin', 'admin'), async (req, res) => {
  try {
    const profile = await UserProfile.findByIdAndDelete(req.params.id);

    if (!profile) {
      return res.status(404).json({
        success: false,
        error: 'Profile not found'
      });
    }

    res.json({
      success: true,
      message: 'Profile deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + error.message
    });
  }
});

// GET download all profiles as ZIP - Protected route (admin only)
router.get('/download', auth, authorize('super_admin', 'admin', 'moderator'), async (req, res) => {
  try {
    // Get all profiles
    const profiles = await UserProfile.find().sort({ createdAt: -1 });

    if (profiles.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'No profiles found to download'
      });
    }

    // Set response headers for zip download
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename="user-profiles-${new Date().toISOString().split('T')[0]}.zip"`);

    // Create archive
    const archive = archiver('zip', {
      zlib: { level: 9 } // Maximum compression
    });

    // Handle archive errors
    archive.on('error', (err) => {
      console.error('Archive error:', err);
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          error: 'Failed to create archive'
        });
      }
    });

    // Pipe archive to response
    archive.pipe(res);

    // Prepare JSON data with image file references
    const profilesData = {
      exportInfo: {
        exportDate: new Date().toISOString(),
        totalProfiles: profiles.length,
        exportedBy: req.user.username,
        version: "1.0"
      },
      profiles: profiles.map((profile, index) => {
        // Extract file extension from base64 data
        let fileExtension = 'jpg'; // default
        if (profile.image.includes('image/png')) {
          fileExtension = 'png';
        } else if (profile.image.includes('image/webp')) {
          fileExtension = 'webp';
        } else if (profile.image.includes('image/jpeg') || profile.image.includes('image/jpg')) {
          fileExtension = 'jpg';
        }

        // Create safe filename
        const safeFileName = profile.name
          .replace(/[^a-zA-Z0-9\s-]/g, '') // Remove special characters
          .replace(/\s+/g, '_') // Replace spaces with underscores
          .toLowerCase();

        const imageFileName = `${String(index + 1).padStart(3, '0')}_${safeFileName}.${fileExtension}`;

        return {
          id: profile._id,
          name: profile.name,
          jobTitle: profile.jobTitle,
          imageFileName: imageFileName,
          imagePath: `images/${imageFileName}`,
          createdAt: profile.createdAt,
          metadata: {
            originalImageFormat: fileExtension,
            profileIndex: index + 1
          }
        };
      })
    };

    // Add JSON file to archive
    archive.append(JSON.stringify(profilesData, null, 2), { name: 'profiles.json' });

    // Add README file
    const readmeContent = `# User Profiles Export

Export Date: ${new Date().toISOString()}
Total Profiles: ${profiles.length}
Exported By: ${req.user.username}

## File Structure:
- profiles.json: Contains all profile data with image file references
- images/: Directory containing all profile images
- README.txt: This file

## profiles.json Structure:
- exportInfo: Metadata about the export
- profiles: Array of profile objects with:
  - id: Unique profile ID
  - name: Person's full name
  - jobTitle: Job title/position
  - imageFileName: Name of the image file
  - imagePath: Relative path to image file
  - createdAt: When profile was created
  - metadata: Additional information

## Usage:
1. Extract the ZIP file
2. Read profiles.json to get profile data
3. Use imagePath or imageFileName to reference the corresponding image files
4. Images are in the 'images/' directory

## Image Naming Convention:
Files are named as: 001_john_doe.jpg, 002_jane_smith.png, etc.
- 3-digit index number
- Underscore-separated name (lowercase)
- Original file extension preserved
`;

    archive.append(readmeContent, { name: 'README.txt' });

    // Add images to archive
    profiles.forEach((profile, index) => {
      try {
        // Extract base64 data (remove data URL prefix)
        const base64Data = profile.image.split(',')[1];
        const imageBuffer = Buffer.from(base64Data, 'base64');

        // Get file extension
        let fileExtension = 'jpg';
        if (profile.image.includes('image/png')) {
          fileExtension = 'png';
        } else if (profile.image.includes('image/webp')) {
          fileExtension = 'webp';
        } else if (profile.image.includes('image/jpeg') || profile.image.includes('image/jpg')) {
          fileExtension = 'jpg';
        }

        // Create safe filename
        const safeFileName = profile.name
          .replace(/[^a-zA-Z0-9\s-]/g, '')
          .replace(/\s+/g, '_')
          .toLowerCase();

        const imageFileName = `${String(index + 1).padStart(3, '0')}_${safeFileName}.${fileExtension}`;

        // Add image to archive
        archive.append(imageBuffer, { name: `images/${imageFileName}` });
      } catch (error) {
        console.error(`Error processing image for profile ${profile.name}:`, error);
      }
    });

    // Finalize archive
    archive.finalize();

  } catch (error) {
    console.error('Download error:', error);
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        error: 'Server Error: ' + error.message
      });
    }
  }
});

module.exports = router;
