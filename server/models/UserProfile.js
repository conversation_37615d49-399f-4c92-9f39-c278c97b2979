const mongoose = require('mongoose');

const userProfileSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  jobTitle: {
    type: String,
    required: [true, 'Job title is required'],
    trim: true,
    maxlength: [100, 'Job title cannot exceed 100 characters']
  },
  image: {
    type: String,
    required: [true, 'Image is required'],
    validate: {
      validator: function(v) {
        return /^data:image\/(jpeg|jpg|png|webp);base64,/.test(v);
      },
      message: 'Invalid image format. Only JPG, PNG, and WebP are supported.'
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('UserProfile', userProfileSchema);
