# 🌐 External API Access - Quick Summary

## ✨ What You Have

Your API uses **Bearer Token Authentication** - a simple, secure way to access your API from any system.

### Key Features:
- ✅ **Permanent Tokens** - Never expire (unless you regenerate them)
- ✅ **Stateless** - No session management needed
- ✅ **Universal** - Works from any device, language, or platform
- ✅ **Secure** - JWT-based with signature verification

---

## 🚀 Quick Start (3 Steps)

### 1️⃣ Find Your Server Address

**Local Network (same WiFi):**
```bash
# On server machine, get IP address:
hostname -I | awk '{print $1}'
# Example output: *************
```

**Production:**
- VPS: `http://your-domain.com` or `http://YOUR_SERVER_IP`
- Vercel: `https://your-app.vercel.app`

### 2️⃣ Get Your Token

```bash
# Replace YOUR_SERVER with your actual server address
curl -X POST YOUR_SERVER/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

**You'll get:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": { ... }
  }
}
```

### 3️⃣ Use Your Token

```bash
# Save token
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# Make API calls
curl -H "Authorization: Bearer $TOKEN" \
  YOUR_SERVER/api/user-profiles
```

---

## 📱 Access Scenarios

### Scenario 1: Testing from Another Computer (Same WiFi)

**Perfect for:** Development, testing, local apps

```bash
# 1. Get server IP (on server machine)
hostname -I

# 2. From another device on same WiFi
curl -X POST http://*************:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# 3. Use the token
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://*************:5000/api/user-profiles
```

**Firewall Setup (if needed):**
```bash
# Linux
sudo ufw allow 5000/tcp

# Windows
netsh advfirewall firewall add rule name="API" dir=in action=allow protocol=TCP localport=5000
```

---

### Scenario 2: Production VPS/Cloud Server

**Perfect for:** Production apps, public APIs, mobile apps

**Setup:**
1. Deploy to VPS (DigitalOcean, AWS, etc.)
2. Use PM2 for process management
3. Add Nginx for reverse proxy
4. Get SSL certificate (Let's Encrypt)

**Quick Deploy:**
```bash
# On VPS
git clone YOUR_REPO
cd resilient-cape-web-admin-dashboard
npm install
pm2 start server/index.js --name "api"
pm2 save
pm2 startup
```

**Access:**
```bash
curl -X POST https://api.yourdomain.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

---

### Scenario 3: Vercel Serverless

**Perfect for:** Quick deployment, auto-scaling, global CDN

**Setup:**
1. Push to GitHub
2. Import on Vercel
3. Add environment variables
4. Deploy!

**Access:**
```bash
curl -X POST https://your-app.vercel.app/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

---

## 💻 Code Examples

### Python
```python
import requests

# Login
response = requests.post(
    "http://*************:5000/api/auth/login",
    json={"username": "admin", "password": "admin123"}
)
token = response.json()["data"]["token"]

# Use token
headers = {"Authorization": f"Bearer {token}"}
profiles = requests.get(
    "http://*************:5000/api/user-profiles",
    headers=headers
)
print(profiles.json())
```

### JavaScript/Node.js
```javascript
const fetch = require('node-fetch');

// Login
const loginRes = await fetch('http://*************:5000/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username: 'admin', password: 'admin123' })
});
const { data } = await loginRes.json();
const token = data.token;

// Use token
const profilesRes = await fetch('http://*************:5000/api/user-profiles', {
    headers: { 'Authorization': `Bearer ${token}` }
});
const profiles = await profilesRes.json();
console.log(profiles);
```

### PHP
```php
<?php
// Login
$ch = curl_init('http://*************:5000/api/auth/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'username' => 'admin',
    'password' => 'admin123'
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = json_decode(curl_exec($ch), true);
$token = $response['data']['token'];

// Use token
$ch = curl_init('http://*************:5000/api/user-profiles');
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Authorization: Bearer ' . $token]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$profiles = json_decode(curl_exec($ch), true);
print_r($profiles);
?>
```

---

## 🧪 Testing Tools

### Built-in Test Script

```bash
# Test from any system
npm run test-external

# Or specify server
npm run test-external http://*************:5000
npm run test-external https://your-app.vercel.app
```

This will:
- ✅ Test server connectivity
- ✅ Login and get token
- ✅ Test authenticated endpoints
- ✅ Verify token validity
- ✅ Save token to file (optional)

### Manual Testing

```bash
# 1. Test health (no auth)
curl http://YOUR_SERVER:5000/api/health

# 2. Login
curl -X POST http://YOUR_SERVER:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# 3. Test authenticated endpoint
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://YOUR_SERVER:5000/api/auth/me
```

---

## 🔒 Security Best Practices

### ✅ DO:
- Store tokens in environment variables
- Use HTTPS in production
- Keep tokens secret (never commit to git)
- Regenerate tokens if compromised
- Use strong passwords

### ❌ DON'T:
- Hardcode tokens in source code
- Share tokens publicly
- Use HTTP in production
- Commit tokens to version control
- Use weak passwords

---

## 🆘 Troubleshooting

| Problem | Solution |
|---------|----------|
| **Connection refused** | Check if server is running: `pm2 status` or `npm run server` |
| **401 Unauthorized** | Verify token format: `Authorization: Bearer TOKEN` |
| **CORS error** | Add your origin to `CORS_ORIGIN` in `.env` |
| **Can't connect from other device** | Check firewall: `sudo ufw allow 5000/tcp` |
| **Token invalid** | Get new token by logging in again |
| **Timeout** | Check network connectivity, ping server |

---

## 📚 Documentation Files

| File | Purpose |
|------|---------|
| **EXTERNAL_API_ACCESS.md** | Detailed guide with all scenarios |
| **QUICK_API_REFERENCE.md** | Quick reference for all endpoints |
| **API_USAGE_GUIDE.md** | Complete API documentation |
| **DEPLOYMENT.md** | Production deployment guide |
| **VERCEL_DEPLOYMENT.md** | Vercel-specific deployment |

---

## 🎯 Common Use Cases

### Mobile App Development
```javascript
// React Native / Flutter / Swift
const API_URL = 'http://*************:5000/api';
const token = await AsyncStorage.getItem('authToken');

fetch(`${API_URL}/user-profiles`, {
    headers: { 'Authorization': `Bearer ${token}` }
});
```

### Python Data Analysis
```python
import pandas as pd
import requests

# Get data from API
headers = {"Authorization": f"Bearer {token}"}
response = requests.get(f"{API_URL}/user-profiles", headers=headers)
data = response.json()['data']

# Convert to DataFrame
df = pd.DataFrame(data)
print(df.head())
```

### Automation Scripts
```bash
#!/bin/bash
# Backup script
TOKEN="your-token-here"
API="http://*************:5000/api"

# Get all profiles
curl -H "Authorization: Bearer $TOKEN" \
  "$API/user-profiles" > backup-$(date +%Y%m%d).json

echo "Backup complete!"
```

### Integration with Other Services
```javascript
// Webhook handler
app.post('/webhook', async (req, res) => {
    // Get data from external service
    const externalData = req.body;
    
    // Send to your API
    await fetch('http://*************:5000/api/user-profiles', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${process.env.API_TOKEN}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(externalData)
    });
    
    res.json({ success: true });
});
```

---

## 🎉 You're Ready!

Your API is now accessible from:
- ✅ Same machine (localhost)
- ✅ Local network devices (WiFi)
- ✅ Internet (with proper deployment)
- ✅ Any programming language
- ✅ Any platform (web, mobile, desktop, IoT)

**Next Steps:**
1. Run `npm run test-external` to test your setup
2. Save your token securely
3. Start building your applications!

For detailed examples and advanced usage, see **EXTERNAL_API_ACCESS.md** and **QUICK_API_REFERENCE.md**.

